/**
 * Semantic search and history management
 *
 * This module provides semantic search functionality for chat history.
 * When Redis messages are provided, the search will only return vector database
 * messages that are older than the oldest Redis message, preventing duplication
 * and ensuring proper chronological ordering of chat history.
 *
 * @module semanticSearch
 */

import { safeQuery } from '../../vectorStore.js';
import { generateEmbedding } from '../geminiAI.js';
import { formatMessage } from '../utils/formatUtils.js';

// Configuration
const SEMANTIC_SEARCH_CONFIG = {
	topK: 5,
	includeMetadata: true,
	minScore: 0.2, // ignore very weak matches
};

/**
 * Performs semantic search and returns formatted history with message IDs
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID to search within
 * @param {string} currentMessageText - The current message text to compare against
 * @param {Array} [redisMessages] - Optional Redis messages to determine oldest timestamp cutoff
 * @returns {Promise<string>} Formatted semantic history
 */
export async function getHistory(env, chatId, currentMessageText, redisMessages = []) {
	let semanticHistoryFormatted = '';

	try {
		if (!currentMessageText) {
			return semanticHistoryFormatted;
		}

		const currentUserMessageEmbedding = await generateEmbedding(env, currentMessageText);
		if (!currentUserMessageEmbedding) {
			console.warn('Could not generate embedding for current message, skipping semantic search.');
			return semanticHistoryFormatted;
		}

		const queryResult = await safeQuery(env, {
			vector: currentUserMessageEmbedding,
			topK: SEMANTIC_SEARCH_CONFIG.topK,
			includeMetadata: SEMANTIC_SEARCH_CONFIG.includeMetadata,
			filter: `chatId = ${chatId}`,
		});

		if (!queryResult?.length) {
			console.log('No relevant messages found in semantic search for this chat.');
			return semanticHistoryFormatted;
		}

		const filteredResults = _filterAndSortResults(queryResult, currentMessageText, redisMessages).filter((m) =>
			typeof m.score === 'number' ? m.score >= SEMANTIC_SEARCH_CONFIG.minScore : true,
		);

		semanticHistoryFormatted = filteredResults.map((match) => formatMessage(match.metadata, env)).join('\n');
		console.log('Semantic search results count (after filtering):', filteredResults.length);
	} catch (error) {
		console.error('Error during semantic search:', error);
	}

	return semanticHistoryFormatted;
}

/**
 * Filters out current message, messages newer than Redis history, and sorts results by timestamp
 * @private
 * @param {Array} results - Search results to filter and sort
 * @param {string} currentMessageText - Current message text to exclude
 * @param {Array} redisMessages - Redis messages to determine oldest timestamp cutoff
 * @returns {Array} Filtered and sorted results
 */
function _filterAndSortResults(results, currentMessageText, redisMessages = []) {
	const normalizedCurrentText = currentMessageText.toLowerCase();

	// Find the oldest timestamp from Redis messages
	let oldestRedisTimestamp = null;
	if (redisMessages.length > 0) {
		// Redis messages have 'date' field, find the minimum
		oldestRedisTimestamp = Math.min(...redisMessages.map((msg) => msg.date || 0));
		console.log(`Filtering semantic search to messages older than Redis oldest: ${oldestRedisTimestamp}`);
	}

	return results
		.filter((match) => {
			if (!match.metadata?.text) return false;

			// Exclude current message
			const normalizedMatchText = match.metadata.text.toLowerCase();
			if (normalizedMatchText === normalizedCurrentText) return false;

			// If we have Redis messages, only include vector messages older than the oldest Redis message
			if (oldestRedisTimestamp !== null) {
				const vectorTimestamp = match.metadata.timestamp || 0;
				if (vectorTimestamp >= oldestRedisTimestamp) {
					return false; // Exclude messages that are newer than or equal to oldest Redis message
				}
			}

			return true;
		})
		.sort((a, b) => (a.metadata.timestamp || 0) - (b.metadata.timestamp || 0));
}
