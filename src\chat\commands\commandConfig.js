/**
 * Command configuration object that maps command types to their trigger strings.
 * This allows for flexible command handling with multiple aliases for the same command.
 *
 * @type {Object<string, string[]>}
 * @property {string[]} CLEAR_HISTORY - Array of command strings that trigger history clearing
 *
 * @example
 * // Adding a new command with multiple aliases:
 * const COMMANDS = {
 *   CLEAR_HISTORY: ['/clear', '/reset', '/start-over'],
 *   HELP: ['/help', '/info']
 * };
 */
export const COMMANDS = {
	// Command for clearing chat history with all associated data
	CLEAR_HISTORY: ['/clear'],
	// Commands for task management
	LIST_TASKS: ['/tasks'],
	COMPLETE_TASK: ['/done'],
	DELETE_TASK: ['/delete_task'],
};