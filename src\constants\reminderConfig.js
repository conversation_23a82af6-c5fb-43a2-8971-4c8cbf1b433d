/**
 * @fileoverview Configuration constants for the enhanced reminder system
 */

/**
 * Default reminder thresholds and intervals
 */
export const REMINDER_THRESHOLDS = {
	// Time-based thresholds (in hours)
	IMMEDIATE: 0.083, // 5 minutes
	URGENT: 1, // 1 hour
	HIGH: 4, // 4 hours
	MEDIUM: 24, // 24 hours
	LOW: 72, // 72 hours (3 days)

	// Maximum reminders per task
	MAX_REMINDERS_PER_TASK: 5,

	// Escalation intervals (hours between reminders)
	ESCALATION_INTERVALS: {
		urgent: [1, 2, 4], // 1h, 2h, 4h
		high: [4, 12, 24], // 4h, 12h, 24h
		medium: [24, 72, 168], // 1d, 3d, 1w
		low: [168, 336], // 1w, 2w
	},
};

/**
 * Default user preferences for reminders
 */
export const DEFAULT_REMINDER_PREFERENCES = {
	timezone: 'UTC',
	preferredReminderTime: '09:00',
	enabledDays: [1, 2, 3, 4, 5], // Monday to Friday
	maxRemindersPerDay: 3,
	reminderTypes: {
		immediate: true,
		daily: true,
		weekly: true,
		beforeDue: true,
		escalating: false,
	},
	quietHours: {
		start: '22:00',
		end: '07:00',
	},
	prioritySettings: {
		urgent: {
			enabled: true,
			ignoreQuietHours: true,
			maxPerDay: 10,
		},
		high: {
			enabled: true,
			ignoreQuietHours: false,
			maxPerDay: 5,
		},
		medium: {
			enabled: true,
			ignoreQuietHours: false,
			maxPerDay: 3,
		},
		low: {
			enabled: true,
			ignoreQuietHours: false,
			maxPerDay: 1,
		},
	},
};

/**
 * Reminder message templates
 */
export const REMINDER_TEMPLATES = {
	urgent: {
		emoji: '🚨',
		title: 'URGENT REMINDER',
		prefix: 'This requires immediate attention:',
		suffix: 'Please handle this as soon as possible!',
	},
	high: {
		emoji: '⚡',
		title: 'High Priority Reminder',
		prefix: 'Important task reminder:',
		suffix: 'This should be completed soon.',
	},
	medium: {
		emoji: '🔔',
		title: 'Friendly Reminder',
		prefix: 'Just a reminder about:',
		suffix: 'When you have a moment, please take care of this.',
	},
	low: {
		emoji: '💭',
		title: 'Gentle Reminder',
		prefix: 'Gentle reminder:',
		suffix: "No rush, but don't forget about this.",
	},
	overdue: {
		emoji: '⚠️',
		title: 'OVERDUE TASK',
		prefix: 'This task is overdue:',
		suffix: 'Please complete this as soon as possible.',
	},
};

/**
 * Cron schedule configurations for different reminder frequencies
 */
export const CRON_SCHEDULES = {
	// Every 15 minutes for urgent tasks
	URGENT: '*/15 * * * *',

	// Every hour for high priority
	HIGH: '0 * * * *',

	// Every 4 hours for medium priority
	MEDIUM: '0 */4 * * *',

	// Twice daily for low priority
	LOW: '0 9,17 * * *',

	// Daily summary at 9 AM
	DAILY_SUMMARY: '0 9 * * *',

	// Weekly summary on Monday at 9 AM
	WEEKLY_SUMMARY: '0 9 * * 1',
};

/**
 * Rate limiting configuration
 */
export const RATE_LIMITS = {
	// Maximum reminders per user per hour
	MAX_REMINDERS_PER_HOUR: 10,

	// Maximum reminders per user per day
	MAX_REMINDERS_PER_DAY: 20,

	// Minimum interval between reminders for same task (minutes)
	MIN_REMINDER_INTERVAL: 30,

	// Maximum concurrent reminder processing
	MAX_CONCURRENT_USERS: 50,
};

/**
 * Task categorization for smart reminders
 */
export const TASK_CATEGORIES = {
	work: {
		defaultPriority: 'medium',
		workingHours: { start: '09:00', end: '17:00' },
		workingDays: [1, 2, 3, 4, 5],
	},
	personal: {
		defaultPriority: 'low',
		workingHours: { start: '08:00', end: '22:00' },
		workingDays: [0, 1, 2, 3, 4, 5, 6],
	},
	health: {
		defaultPriority: 'high',
		workingHours: { start: '07:00', end: '21:00' },
		workingDays: [0, 1, 2, 3, 4, 5, 6],
	},
	urgent: {
		defaultPriority: 'urgent',
		workingHours: { start: '00:00', end: '23:59' },
		workingDays: [0, 1, 2, 3, 4, 5, 6],
	},
	shopping: {
		defaultPriority: 'low',
		workingHours: { start: '09:00', end: '20:00' },
		workingDays: [0, 1, 2, 3, 4, 5, 6],
	},
};

/**
 * Smart reminder features configuration
 */
export const SMART_FEATURES = {
	// Enable context-aware reminders
	CONTEXT_AWARE: true,

	// Enable location-based reminders (if location data available)
	LOCATION_BASED: false,

	// Enable time-of-day optimization
	TIME_OPTIMIZATION: true,

	// Enable workload balancing
	WORKLOAD_BALANCING: true,

	// Enable reminder clustering (group similar tasks)
	CLUSTERING: true,

	// Enable adaptive timing (learn from user behavior)
	ADAPTIVE_TIMING: false,
};

/**
 * Integration settings
 */
export const INTEGRATION_SETTINGS = {
	// Telegram-specific settings
	telegram: {
		useInlineKeyboards: true,
		enableCallbacks: true,
		maxMessageLength: 4096,
		parseMode: 'HTML',
	},

	// Redis key prefixes
	redis: {
		userPreferences: 'user_reminder_preferences',
		reminderQueue: 'reminder_queue',
		reminderHistory: 'reminder_history',
		rateLimits: 'reminder_rate_limits',
	},
};

/**
 * Validation rules for user preferences
 */
export const VALIDATION_RULES = {
	timezone: {
		// List of valid timezone identifiers (limited for simplicity).
		// For full support, use a library like moment-timezone to validate timezones dynamically.
		validTimezones: [
			'UTC',
			'America/New_York',
			'America/Los_Angeles',
			'America/Chicago',
			'Europe/London',
			'Europe/Paris',
			'Europe/Berlin',
			'Asia/Tokyo',
			'Asia/Shanghai',
			'Asia/Kolkata',
			'Asia/Bangkok',
			'Australia/Sydney',
		],
		// Note: This list is not exhaustive. Consider using moment-timezone for comprehensive validation.
	},

	time: {
		// 24-hour format HH:MM
		pattern: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
	},

	days: {
		// Valid day numbers (0 = Sunday, 6 = Saturday)
		validDays: [0, 1, 2, 3, 4, 5, 6],
	},

	priority: {
		validPriorities: ['low', 'medium', 'high', 'urgent'],
	},

	frequency: {
		validFrequencies: ['immediate', 'hourly', 'daily', 'weekly', 'custom'],
	},
};

/**
 * Error messages for reminder system
 */
export const ERROR_MESSAGES = {
	INVALID_TIMEZONE: 'Invalid timezone. Please use a valid timezone identifier.',
	INVALID_TIME_FORMAT: 'Invalid time format. Please use HH:MM format (24-hour).',
	INVALID_DAYS: 'Invalid days selection. Please use numbers 0-6 (0=Sunday, 6=Saturday).',
	RATE_LIMIT_EXCEEDED: 'Too many reminders sent. Please wait before requesting more.',
	TASK_NOT_FOUND: 'Task not found or already completed.',
	PREFERENCES_UPDATE_FAILED: 'Failed to update reminder preferences.',
	REMINDER_SEND_FAILED: 'Failed to send reminder notification.',
};
