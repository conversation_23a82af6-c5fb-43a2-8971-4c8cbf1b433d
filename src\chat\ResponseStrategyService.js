export class ResponseStrategyService {
	constructor() {
		// Could be extended to load strategies from config
	}

	getResponseStrategy(conversationState) {
		const strategy = {
			shouldAskClarification: false,
			shouldSuggestTopic: false,
			shouldBeProactive: false,
			responseStyle: 'normal',
			additionalContext: '',
		};

		switch (conversationState?.state) {
			case 'greeting':
				strategy.responseStyle = 'welcoming';
				strategy.additionalContext = 'User is starting a conversation, be warm and inviting';
				break;

			case 'stalled':
				strategy.shouldSuggestTopic = true;
				strategy.shouldBeProactive = true;
				strategy.additionalContext = 'Conversation seems stalled, gently suggest new topics or ask engaging questions';
				break;

			case 'needs_followup':
				strategy.shouldBeProactive = true;
				strategy.additionalContext = 'Check on previous discussions or offer assistance';
				break;

			case 'escalation_needed':
				strategy.shouldAskClarification = true;
				strategy.responseStyle = 'helpful';
				strategy.additionalContext = 'User may need additional help, be extra supportive';
				break;

			default: // ongoing
				strategy.responseStyle = 'normal';
				strategy.additionalContext = 'Continue the conversation';
				break;
		}

		return strategy;
	}
}
