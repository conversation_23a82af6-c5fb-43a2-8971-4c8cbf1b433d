import { GeminiAIError } from '../errors/GeminiErrors.js';
import { apiKeyManager } from './globalInstances.js';
import { embeddingManager } from './globalInstances.js';
import { FastProvider } from './providers/FastProvider.js';
import { GeminiProvider } from './providers/GeminiProvider.js';
import { ErrorAggregator } from './utils/ErrorAggregator.js';

/**
 * Legacy function for backward compatibility
 * @param {object} env - Environment variables containing API keys
 * @returns {string} The selected API key
 * @throws {ApiKeyError} If no API keys are configured
 */
export function getNextGeminiApiKey(env) {
	return apiKeyManager.getNextGeminiApiKey(env);
}

/**
 * Calls the Generative AI model with fallback logic using LangChain.
 * Now includes fallback to fast providers if all Gemini attempts fail.
 * @param {object} env - Environment variables
 * @param {object} config - AI configuration object
 * @param {Array} contents - Array of message contents
 * @returns {Promise<object>} Response object with text and thoughts
 * @throws {GeminiAIError} If all models and API keys fail
 */
export async function callGenerativeAI(env, config, contents) {
	try {
		const errorAggregator = new ErrorAggregator();
		const geminiProvider = new GeminiProvider(apiKeyManager);

		// First, try all Gemini models and API keys
		const geminiResult = await geminiProvider.attemptCompletion(env, config, contents, errorAggregator);
		if (geminiResult) {
			return geminiResult;
		}

		// If all Gemini attempts failed, try fast providers as fallback
		console.warn('All Gemini models failed, attempting fallback to fast providers...');
		try {
			// Create a simplified config for fast providers (remove tools that aren't supported)
			const fallbackConfig = {
				...config,
				tools: undefined, // Fast providers don't support tools
			};

			const fastProvider = new FastProvider();
			const fastErrorAggregator = new ErrorAggregator();
			const fastResult = await fastProvider.attemptCompletion(env, fallbackConfig, contents, fastErrorAggregator);

			if (fastResult) {
				console.log('✅ Successfully fell back to fast provider');
				// Return in the same format as Gemini results
				return {
					text: fastResult.text,
					thoughts: '', // Fast providers don't provide thoughts
				};
			}

			// Both Gemini and fast providers failed
			const geminiErrors = errorAggregator.createErrorMessage('Gemini providers');
			const fastErrors = fastErrorAggregator.createErrorMessage('Fast providers');

			throw new GeminiAIError(
				`All Gemini models and fast provider fallbacks failed. ${geminiErrors}. ${fastErrors}`,
				errorAggregator.getLastError(),
			);
		} catch (fastError) {
			console.error('Fast provider fallback also failed:', fastError);

			// If the fast error is already a GeminiAIError with aggregated info, throw it
			if (fastError instanceof GeminiAIError) {
				throw fastError;
			}

			// Otherwise, create a new error with both failure details
			throw new GeminiAIError(
				`All Gemini models and fast provider fallbacks failed. Gemini: ${errorAggregator.createErrorMessage('Gemini')}. Fast providers: ${
					fastError.message
				}`,
				errorAggregator.getLastError(),
			);
		}
	} catch (error) {
		if (error instanceof GeminiAIError) {
			throw error;
		}
		throw new GeminiAIError(`Unexpected error in callGenerativeAI: ${error.message}`, error);
	}
}

/**
 * Gets the provider priority order, starting with preferred provider
 * @param {string} preferredProvider - The preferred provider to try first
 * @param {object} availableProviders - Object containing all available providers
 * @returns {string[]} Array of provider names in priority order
 */
export function getProviderPriorityOrder(preferredProvider, availableProviders) {
	const allProviders = Object.keys(availableProviders);
	const priorityOrder = [];

	// Add preferred provider first if it exists
	if (preferredProvider && allProviders.includes(preferredProvider)) {
		priorityOrder.push(preferredProvider);
	}

	// Add remaining providers
	allProviders.forEach((provider) => {
		if (provider !== preferredProvider) {
			priorityOrder.push(provider);
		}
	});

	return priorityOrder;
}

/**
 * Calls the fast Generative AI model with fallback logic using LangChain.
 * Now tries all available providers if the preferred one fails.
 * @param {object} env - Environment variables
 * @param {object} config - AI configuration object
 * @param {Array} contents - Array of message contents
 * @returns {Promise<object>} Response object with text
 * @throws {GeminiAIError} If all providers and models fail
 */
export async function callFastGenerativeAI(env, config, contents) {
	try {
		const fastProvider = new FastProvider();
		const errorAggregator = new ErrorAggregator();

		const result = await fastProvider.attemptCompletion(env, config, contents, errorAggregator);

		if (result) {
			return result;
		}

		// All providers failed
		throw new GeminiAIError(errorAggregator.createErrorMessage('All fast providers and models failed'), errorAggregator.getLastError());
	} catch (error) {
		if (error instanceof GeminiAIError) {
			throw error;
		}
		throw new GeminiAIError(`Unexpected error in callFastGenerativeAI: ${error.message}`, error);
	}
}

/**
 * Legacy function for backward compatibility
 * @param {object} env - Environment variables
 * @param {string} textToEmbed - Text to generate embedding for
 * @returns {Promise<number[]>} Embedding vector
 * @throws {GeminiAIError} If API key is missing or text is invalid
 */
export async function generateEmbedding(env, textToEmbed) {
	return embeddingManager.generateEmbedding(env, textToEmbed);
}
