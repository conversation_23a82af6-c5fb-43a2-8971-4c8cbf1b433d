import { sendLongTelegramMessage } from '../../chat/telegram/index.js';
import { escapeHtml } from '../../chat/telegramUtils.js';
import { updateRateLimit } from './rateLimiter.js';

/**
 * Group tasks by priority
 *
 * Groups an array of tasks into a dictionary where the keys are priority levels
 * and the values are arrays of tasks with that priority. Tasks without a priority
 * are assigned to 'medium' by default.
 *
 * @param {Array} tasks - Array of task objects to group
 * @returns {Object} Dictionary with priority levels as keys and arrays of tasks as values
 */
export function groupTasksByPriority(tasks) {
	return tasks.reduce((groups, task) => {
		const priority = task.priority || 'medium';
		if (!groups[priority]) groups[priority] = [];
		groups[priority].push(task);
		return groups;
	}, {});
}

/**
 * Send reminders for tasks of a specific priority
 *
 * Sends reminder messages for tasks of a specific priority level to a user.
 * Creates an appropriate message and keyboard, sends it via Telegram, and
 * updates the reminder status for all tasks.
 *
 * @param {TaskManager} taskManager - The TaskManager instance for updating task status
 * @param {string} userId - The ID of the user to send reminders to
 * @param {string} priority - The priority level of the tasks ('urgent', 'high', 'medium', 'low')
 * @param {Array} tasks - Array of task objects to remind about
 * @param {Object} env - The environment object containing configuration and bindings
 * @param {Object} redisClient - Redis client for rate limiting
 * @returns {Promise<void>}
 */
export async function sendPriorityReminders(taskManager, userId, priority, tasks, env, redisClient) {
	try {
		const message = createReminderMessage(priority, tasks);

		await sendLongTelegramMessage(env, userId, message, {
			parseMode: 'HTML',
			reply_markup: createReminderKeyboard(tasks),
		});

		// Update reminder status for all tasks
		for (const task of tasks) {
			await taskManager.updateTaskReminder(userId, task.id, true);
		}

		// Update rate limit counters
		// Note: This would normally be imported, but we're passing it as a parameter
		// to avoid circular dependencies
		if (redisClient && typeof updateRateLimit === 'function') {
			await updateRateLimit(redisClient, userId);
		}

		console.log(`Sent ${priority} priority reminders to user ${userId} for ${tasks.length} tasks`);
	} catch (error) {
		console.error(`Failed to send ${priority} reminders to user ${userId}:`, error);
	}
}

/**
 * Create contextual reminder message based on priority and tasks
 *
 * Creates a formatted message for task reminders with appropriate emoji,
 * title, and content based on the priority level and number of tasks.
 * Includes due dates, overdue status, and estimated duration when available.
 * Uses HTML formatting for Telegram messages.
 *
 * @param {string} priority - The priority level of the tasks ('urgent', 'high', 'medium', 'low')
 * @param {Array} tasks - Array of task objects to include in the reminder
 * @returns {string} Formatted message string ready for Telegram
 */
export function createReminderMessage(priority, tasks) {
	const priorityEmojis = {
		urgent: '🚨',
		high: '⚡',
		medium: '🔔',
		low: '💭',
	};

	const priorityTitles = {
		urgent: 'URGENT REMINDER',
		high: 'High Priority Reminder',
		medium: 'Friendly Reminder',
		low: 'Gentle Reminder',
	};

	const emoji = priorityEmojis[priority] || '🔔';
	const title = priorityTitles[priority] || 'Reminder';

	let message = `${emoji} <b>${title}</b>\n\n`;

	if (tasks.length === 1) {
		const task = tasks[0];
		message += `${escapeHtml(task.description)}`;

		if (task.dueDate) {
			const dueDate = new Date(task.dueDate);
			const isOverdue = dueDate < new Date();
			const dueDateStr = dueDate.toLocaleDateString();

			if (isOverdue) {
				message += `\n\n⚠️ <b>Overdue since:</b> ${escapeHtml(dueDateStr)}`;
			} else {
				message += `\n\n📅 <b>Due:</b> ${escapeHtml(dueDateStr)}`;
			}
		}

		if (task.metadata?.estimatedDuration) {
			message += `\n⏱️ <b>Estimated time:</b> ${escapeHtml(task.metadata.estimatedDuration)}`;
		}
	} else {
		message += `You have ${tasks.length} ${priority} priority tasks:\n\n`;
		tasks.forEach((task, index) => {
			message += `${index + 1}. ${escapeHtml(task.description)}\n`;
		});
	}

	// Add instruction for user actions with proper HTML formatting
	message += `\n\n<i>Reply with /done [task] to mark as complete, or /snooze [task] to remind later.</i>`;

	return message;
}

/**
 * Create inline keyboard for reminder actions
 *
 * Creates an inline keyboard for Telegram messages with appropriate actions
 * based on the number of tasks. For a single task, provides specific actions
 * (complete, snooze options). For multiple tasks, provides general actions
 * (view all tasks, reminder settings).
 *
 * @param {Array} tasks - Array of task objects to create actions for
 * @returns {Object} Telegram inline keyboard object
 */
export function createReminderKeyboard(tasks) {
	if (tasks.length === 1) {
		const task = tasks[0];
		return {
			inline_keyboard: [
				[
					{ text: '✅ Complete', callback_data: `complete_${task.id}` },
					{ text: '⏰ Snooze 1h', callback_data: `snooze_${task.id}_60` },
				],
				[
					{ text: '⏰ Snooze 4h', callback_data: `snooze_${task.id}_240` },
					{ text: '⏰ Snooze 1d', callback_data: `snooze_${task.id}_1440` },
				],
			],
		};
	}

	// For multiple tasks, provide general actions
	return {
		inline_keyboard: [
			[
				{ text: '📋 View All Tasks', callback_data: 'list_tasks' },
				{ text: '⚙️ Reminder Settings', callback_data: 'reminder_settings' },
			],
		],
	};
}
