import { COMMANDS } from './commandConfig.js';

/**
 * Detects if a given text contains a valid command.
 *
 * This function checks if the input text matches any registered command exactly
 * or if it starts with a command followed by a space (allowing for command arguments).
 * The comparison is case-insensitive.
 *
 * @param {string|null|undefined} text - The text to check for commands
 * @returns {string|null} The command type if found (e.g., 'CLEAR_HISTORY'), null otherwise
 *
 * @example
 * detectCommand('/clear') // returns 'CLEAR_HISTORY'
 * detectCommand('/clear some arguments') // returns 'CLEAR_HISTORY'
 * detectCommand('/unknown') // returns null
 * detectCommand('') // returns null
 */
export function detectCommand(text) {
	// Validate input - ensure text is a non-empty string
	if (!text || typeof text !== 'string') {
		return null;
	}

	// Convert to lowercase once for efficient comparison
	// This optimization avoids repeated case conversion in loops
	const trimmedText = text.toLowerCase().trim();

	// Check each command type and its associated command strings
	for (const commandType in COMMANDS) {
		for (const command of COMMANDS[commandType]) {
			const lowerCaseCommand = command.toLowerCase();

			// Match exact command or command followed by space (with arguments)
			// This allows commands to have additional parameters
			if (trimmedText === lowerCaseCommand || trimmedText.startsWith(lowerCaseCommand + ' ')) {
				return commandType;
			}
		}
	}

	// No command detected
	return null;
}