/**
 * @fileoverview Embedding generation manager with caching and error handling.
 */

import { <PERSON><PERSON><PERSON><PERSON>Error, GeminiAIError } from '../errors/GeminiErrors.js';
import { ModelFactory } from '../factories/ModelFactory.js';

/**
 * Manages embedding generation with caching and error handling
 */
export class EmbeddingManager {
	constructor(apiKeyManager) {
		this.apiKeyManager = apiKeyManager;
		this.embeddingsInstance = null;
		this.currentApiKey = null;
		// Simple LRU-like cache with TTL and size cap for embeddings
		this.embeddingCache = new Map(); // key -> { v: number[], t: number }
		this.cacheMaxSize = 2000;
		this.cacheTtlMs = 1000 * 60 * 60; // 1 hour
		this.pendingEmbeddings = new Map(); // Track pending embedding requests
	}

	/**
	 * Creates or reuses an embeddings instance
	 * @param {string} apiKey - Valid Gemini API key
	 * @returns {GoogleGenerativeAIEmbeddings} Embeddings instance
	 */
	getEmbeddingsInstance(apiKey) {
		if (!this.embeddingsInstance || this.currentApiKey !== apiKey) {
			this.embeddingsInstance = ModelFactory.createEmbeddingsInstance(apiKey);
			this.currentApiKey = apiKey;
		}
		return this.embeddingsInstance;
	}

	/**
	 * Generates an embedding for the given text with enhanced error handling and API key rotation
	 * @param {object} env - Environment variables
	 * @param {string} textToEmbed - Text to generate embedding for
	 * @returns {Promise<number[]>} Embedding vector
	 * @throws {GeminiAIError} If API key is missing or text is invalid
	 */
	async generateEmbedding(env, textToEmbed) {
		// Input validation
		if (!textToEmbed || typeof textToEmbed !== 'string' || textToEmbed.length === 0) {
			throw new GeminiAIError('Invalid text input for embedding: must be a non-empty string');
		}

		if (!env) {
			throw new GeminiAIError('Environment variables are required');
		}

		// Create cache key
		const cacheKey = this._createCacheKey(textToEmbed);

		// Check cache first
		const cached = this.embeddingCache.get(cacheKey);
		if (cached && Date.now() - cached.t < this.cacheTtlMs) {
			// touch to keep recent
			this.embeddingCache.delete(cacheKey);
			this.embeddingCache.set(cacheKey, cached);
			return cached.v;
		}

		// Check if this embedding is already being generated
		if (this.pendingEmbeddings.has(cacheKey)) {
			console.log('[EmbeddingManager] Waiting for pending embedding request');
			return await this.pendingEmbeddings.get(cacheKey);
		}

		// Create promise for this embedding request
		const embeddingPromise = this._generateEmbeddingInternal(env, textToEmbed);
		this.pendingEmbeddings.set(cacheKey, embeddingPromise);

		try {
			const result = await embeddingPromise;
			// Cache the result with TTL and size enforcement
			this._cacheSet(cacheKey, result);
			return result;
		} finally {
			// Clean up pending request
			this.pendingEmbeddings.delete(cacheKey);
		}
	}

	/**
	 * Internal method to generate embedding
	 * @private
	 */
	async _generateEmbeddingInternal(env, textToEmbed) {
		// Normalize text (trim and collapse whitespace) to improve cache hit rate
		textToEmbed = String(textToEmbed).trim().replace(/\s+/g, ' ');

		// Load available API keys
		const apiKeys = this.apiKeyManager.loadGeminiApiKeys(env);
		if (apiKeys.length === 0) {
			throw new ApiKeyError('No Gemini API keys configured in environment variables');
		}

		let lastError = null;
		let attempts = 0;
		const maxAttempts = 1; // Try only once

		// Try only once
		while (attempts < maxAttempts) {
			try {
				const apiKey = this.apiKeyManager.getNextGeminiApiKey(env);
				if (!apiKey) {
					throw new ApiKeyError('No Generative AI API key available for embedding');
				}

				// Create or reuse embeddings instance
				const embeddings = this.getEmbeddingsInstance(apiKey);

				// Generate embedding with enhanced context
				const result = await embeddings.embedQuery(textToEmbed);

				// Add basic validation for result
				if (!result || !Array.isArray(result) || result.length === 0) {
					throw new GeminiAIError('Empty or invalid embedding response from API');
				}

				// Success - return the result
				return result;
			} catch (error) {
				attempts++;
				lastError = error;

				// Enhanced error logging with context
				const errorContext = {
					textLength: textToEmbed?.length || 0,
					error: error.message,
					timestamp: new Date().toISOString(),
					attempt: attempts,
					maxAttempts: maxAttempts,
				};

				console.error('Embedding generation failed:', errorContext);

				// If this is a GeminiAIError (validation error), don't retry
				if (error instanceof GeminiAIError) {
					throw error;
				}

				// Update API key status for API-related errors
				if (this.currentApiKey) {
					this.apiKeyManager.updateKeyStatus(this.currentApiKey, error);
				}

				// If we've exhausted all attempts, break the loop
				if (attempts >= maxAttempts) {
					break;
				}

				// Log retry attempt
				console.log(`Retrying embedding generation with next API key (attempt ${attempts + 1}/${maxAttempts})`);
			}
		}

		// All attempts failed
		throw new GeminiAIError(
			`Failed to generate embedding after ${attempts} attempts: ${lastError?.message || 'Unknown error'}`,
			lastError,
		);
	}

	/**
	 * Create cache key for text
	 * @private
	 */
	_createCacheKey(text) {
		// Simple hash function for cache key
		let hash = 0;
		for (let i = 0; i < text.length; i++) {
			const char = text.charCodeAt(i);
			hash = (hash << 5) - hash + char;
			hash = hash & hash; // Convert to 32-bit integer
		}
		return `embed_${hash}_${text.length}`;
	}

	/**
	 * Clear embedding cache
	 */
	clearCache() {
		this.embeddingCache.clear();
		console.log('[EmbeddingManager] Cache cleared');
	}

	/**
	 * Batch generate embeddings for multiple texts efficiently with caching/coalescing.
	 * @param {object} env
	 * @param {string[]} texts
	 * @returns {Promise<number[][]>}
	 */
	async generateEmbeddings(env, texts) {
		if (!Array.isArray(texts) || texts.length === 0) return [];
		// Normalize texts and prepare cache keys
		const normalized = texts.map((t) => (typeof t === 'string' ? t.trim().replace(/\s+/g, ' ') : ''));
		const keys = normalized.map((t) => this._createCacheKey(t));

		// Resolve cached and pending first
		const results = new Array(texts.length);
		const toCompute = [];
		for (let i = 0; i < keys.length; i++) {
			const k = keys[i];
			const cached = this.embeddingCache.get(k);
			if (cached && Date.now() - cached.t < this.cacheTtlMs) {
				results[i] = cached.v;
				continue;
			}
			if (this.pendingEmbeddings.has(k)) {
				toCompute.push({ index: i, key: k, promise: this.pendingEmbeddings.get(k) });
				continue;
			}
			toCompute.push({ index: i, key: k, text: normalized[i] });
		}

		// Kick off new computations serially (API may not support batch). Could add limited concurrency if safe.
		for (const item of toCompute) {
			if (item.promise) continue;
			const p = this._generateEmbeddingInternal(env, item.text).then((vec) => {
				this._cacheSet(item.key, vec);
				return vec;
			});
			this.pendingEmbeddings.set(item.key, p);
			item.promise = p;
		}

		// Await all promises in original order
		for (const item of toCompute) {
			results[item.index] = await item.promise.finally(() => this.pendingEmbeddings.delete(item.key));
		}

		return results;
	}

	// Internal cache setter that enforces LRU-ish eviction and TTL
	_cacheSet(key, value) {
		// Evict expired entries opportunistically
		for (const [k, v] of this.embeddingCache) {
			if (Date.now() - v.t >= this.cacheTtlMs) {
				this.embeddingCache.delete(k);
			}
		}
		// Enforce size limit (remove oldest insertion order item)
		if (this.embeddingCache.size >= this.cacheMaxSize) {
			const firstKey = this.embeddingCache.keys().next().value;
			if (firstKey !== undefined) this.embeddingCache.delete(firstKey);
		}
		this.embeddingCache.set(key, { v: value, t: Date.now() });
	}

	/**
	 * Get cache statistics
	 */
	getCacheStats() {
		return {
			cacheSize: this.embeddingCache.size,
			pendingRequests: this.pendingEmbeddings.size,
		};
	}
}
