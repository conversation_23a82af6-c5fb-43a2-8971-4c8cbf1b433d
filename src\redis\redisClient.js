import { Redis } from '@upstash/redis/cloudflare';

/**
 * Manages Redis client instances to ensure that only one client is created per instance ID.
 */
class RedisClientManager {
	constructor() {
		/** @type {Map<string, Redis>} */
		this.clients = new Map();
	}

	/**
	 * Returns a Redis client for the given environment.
	 * If a client for the current instance ID already exists, it returns the existing client.
	 * Otherwise, it creates a new client and stores it for future use.
	 *
	 * @param {object} env - The environment variables.
	 * @returns {Redis} - The Redis client.
	 */
	getClient(env) {
		const key = env.CF_INSTANCE_ID || 'default';
		if (!this.clients.has(key)) {
			try {
				const client = Redis.fromEnv(env);
				this.clients.set(key, client);
			} catch (error) {
				console.error(`[RedisClientManager] Failed to create Redis client for instance: ${key}`, error);
				throw new Error(`Could not create Redis client: ${error.message}`);
			}
		}
		return this.clients.get(key);
	}
}

const manager = new RedisClientManager();

/**
 * Returns a Redis client for the given environment.
 * This is a convenience function that uses a singleton instance of RedisClientManager.
 *
 * @param {object} env - The environment variables.
 * @returns {Redis} - The Redis client.
 */
export const getRedisClient = (env) => manager.getClient(env);
