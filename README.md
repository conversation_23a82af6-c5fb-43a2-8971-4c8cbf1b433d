# Harmony Chat

A high-performance, AI-powered Telegram bot built on Cloudflare Workers. This bot, named <PERSON>, uses Google's Gemini AI to provide context-aware, conversational responses. It integrates with Redis for conversation history and includes features like media processing and proactive messaging.

## Features

- **AI-Powered Conversations**: Uses Google Gemini AI with a custom personality (Harmony).
- **Context-Aware Responses**: Maintains conversation history and user facts for meaningful interactions.
- **Media Processing**: Supports photos, documents, and media groups.
- **Proactive Messaging**: Can generate and send its own "thoughts" or monologues to a chat.
- **User Whitelist**: Configurable access control to restrict bot usage to authorized users only.
- **Smart Message Filtering**: Processes mentions, private messages, and media content.
- **Typing Indicators**: Shows realistic typing status during processing.
- **Multi-language Support**: Primarily Indonesian with organic Japanese phrases.
- **Serverless Architecture**: Built on Cloudflare Workers for scalability and performance.
- **Intelligent Caching**: Redis-based caching for improved performance.
- **Health Monitoring**: Built-in health check endpoints.
- **Vector Storage**: Uses Upstash Vector for semantic search and message embedding.
- **Error Handling**: Improved error handling with user-friendly messages and admin notifications.
- **Fact Extraction**: Extracts and stores user facts for personalized interactions.
- **Command Handling**: Detects and processes commands in messages.

## API Endpoints

### Core Endpoints

#### `GET /health`

Health check endpoint for monitoring.

**Response:**

```json
{
	"status": "healthy",
	"timestamp": "2024-01-01T00:00:00.000Z",
	"environment": "production"
}
```

#### `GET /ping`

Simple ping endpoint.

**Response:** `pong`

### Telegram Integration

#### `POST /hrmny`

The primary webhook endpoint for Telegram bot integration, featuring AI-powered conversational responses.

**Features:**

- **AI-Powered Conversations**: Uses Google Gemini AI with a custom personality (Harmony).
- **Multi-language Support**: Primarily Indonesian with organic Japanese phrases.
- **Context-Aware Responses**: Maintains conversation history and user facts.
- **Media Processing**: Supports photos, documents, and media groups.
- **Smart Message Filtering**: Processes mentions, private messages, and media content.
- **Typing Indicators**: Shows realistic typing status during processing.
- **Reply Context**: Understands and responds to replied messages.
- **User Fact Learning**: Remembers and updates user information over time.
- **Command Detection**: Processes commands in messages.

#### `POST /thoughts`

An endpoint that allows the AI to generate and send a "monologue" to a specified Telegram chat, enabling proactive messaging.

## Deployment

This project is deployed on Cloudflare Workers using Wrangler.

### Requirements

- Node.js 18+
- npm or yarn
- Cloudflare account with Workers enabled
- Upstash Redis instance
- Telegram bot token

### Environment Variables

Configure these in your `wrangler.toml`:

```toml
[vars]
DEV_MODE = "false"
PROTOCOL = "https"
HOSTNAME = "your-worker.workers.dev"
TELEGRAM_BOT_USERNAME = "your_bot"
# User whitelist (comma-separated user IDs)
WHITELISTED_USERS = "*********,*********"
# Or disable whitelist entirely
# DISABLE_USER_WHITELIST = "true"
```

For detailed whitelist configuration, see [docs/USER_WHITELIST.md](docs/USER_WHITELIST.md).

### Setup

1.  **Clone the repository:**

    ```sh
    git clone <repository-url>
    cd harmony-chat
    ```

2.  **Install dependencies:**

    ```sh
    npm install
    ```

3.  **Configure Wrangler:**
    - Update `wrangler.toml` with your Cloudflare account details.
    - Set up environment variables and secrets for your Telegram bot token and Upstash Redis credentials.

4.  **Deploy:**
    ```sh
    npm run deploy
    ```

### Development

To start the development server:

```sh
npm run dev
```

For public development (accessible from external networks):

```sh
npm run dev:public
```

## Architecture

- **Framework**: Hono.js for lightweight, fast routing.
- **Runtime**: Cloudflare Workers.
- **AI Integration**: Google Gemini AI for Telegram bot responses, orchestrated with LangChain.
- **Database**: Upstash Redis for caching and conversation history, and Upstash Vector for semantic search.
- **Monitoring**: Langsmith for tracing and debugging.
- **Error Handling**: Improved error handling with user-friendly messages and admin notifications.
- **Fact Extraction**: Extracts and stores user facts for personalized interactions.

## License

This project is private and proprietary.
