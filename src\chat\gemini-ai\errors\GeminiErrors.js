/**
 * @fileoverview Custom error classes for the GeminiAI module.
 */

/**
 * Base error class for GeminiAI-related errors
 */
export class GeminiAIError extends Error {
	constructor(message, cause = null) {
		super(message);
		this.name = 'GeminiAIError';
		this.cause = cause;
	}
}

/**
 * Error class for API key-related issues
 */
export class ApiKeyError extends GeminiAIError {
	constructor(message) {
		super(message);
		this.name = 'ApiKeyError';
	}
}

/**
 * Error class for model-related issues
 */
export class ModelError extends GeminiAIError {
	constructor(message, model) {
		super(message);
		this.name = 'ModelError';
		this.model = model;
	}
}
