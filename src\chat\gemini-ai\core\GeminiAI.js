import { EmbeddingManager } from '../managers/EmbeddingManager.js';
import { SimpleApiKeyManager } from '../managers/SimpleApiKeyManager.js';
import { callFastGenerativeAI, callGenerativeAI } from './api.js';

/**
 * Main GeminiAI class that encapsulates all AI functionality
 */
export class GeminiAI {
	constructor() {
		this.apiKeyManager = new SimpleApiKeyManager();
		this.embeddingManager = new EmbeddingManager(this.apiKeyManager);
	}

	/**
	 * Calls the Generative AI model with fallback logic
	 * @param {object} env - Environment variables
	 * @param {object} config - AI configuration object
	 * @param {Array} contents - Array of message contents
	 * @returns {Promise<object>} Response object with text and thoughts
	 */
	async callGenerativeAI(env, config, contents) {
		return callGenerativeAI(env, config, contents);
	}

	/**
	 * Calls the fast Generative AI model
	 * @param {object} env - Environment variables
	 * @param {object} config - AI configuration object
	 * @param {Array} contents - Array of message contents
	 * @returns {Promise<object>} Response object with text
	 */
	async callFastGenerativeAI(env, config, contents) {
		return callFastGenerativeAI(env, config, contents);
	}

	/**
	 * Generates an embedding for the given text
	 * @param {object} env - Environment variables
	 * @param {string} textToEmbed - Text to generate embedding for
	 * @returns {Promise<number[]>} Embedding vector
	 */
	async generateEmbedding(env, textToEmbed) {
		return this.embeddingManager.generateEmbedding(env, textToEmbed);
	}

	/**
	 * Gets the next API key in rotation
	 * @param {object} env - Environment variables
	 * @returns {string} The selected API key
	 */
	getNextGeminiApiKey(env) {
		return this.apiKeyManager.getNextGeminiApiKey(env);
	}
}
