import { getRedisClient } from '../../../redis/redisClient.js';
import { TaskManager } from '../../tasks/taskManager.js';
import { sendLongTelegramMessage } from '../../telegram/index.js';
import { escapeHtml } from '../../telegramUtils.js';

/**
 * Handles the list tasks command by retrieving and displaying user's pending tasks.
 *
 * @param {Object} env - Environment variables
 * @param {string} chatId - The ID of the chat
 * @param {string} userId - The ID of the user
 * @returns {Promise<boolean>} True if the command was processed successfully, false otherwise
 */
export async function handleListTasksCommand(env, chatId, userId) {
	try {
		const redisClient = getRedisClient(env);
		const taskManager = new TaskManager(redisClient);
		const tasks = await taskManager.getTasks(userId);
		const pendingTasks = tasks.filter((task) => task.status === 'pending');

		let responseMessage;
		if (pendingTasks.length === 0) {
			responseMessage = 'You have no pending tasks! ✨';
		} else {
			const taskList = pendingTasks.map((task, index) => `${index + 1}. ${escapeHtml(task.description)}`).join('\n');
			responseMessage = `<b>Your Pending Tasks:</b>\n${taskList}`;
		}

		await sendLongTelegramMessage(env, chatId, responseMessage, { parseMode: 'HTML' });
		return true;
	} catch (error) {
		console.error(`Error handling /tasks command for chat ${chatId}:`, error);
		await sendLongTelegramMessage(env, chatId, "❌ Sorry, I couldn't retrieve your tasks right now.", { parseMode: 'HTML' });
		return false;
	}
}