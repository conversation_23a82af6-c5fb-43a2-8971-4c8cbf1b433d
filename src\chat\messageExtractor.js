import { checkBotMention } from './telegramUtils.js';

/**
 * Extracts and normalizes relevant data from the Telegram webhook payload.
 * This function processes both direct messages and callback queries, handling
 * various content types including text, photos, and documents.
 *
 * @param {Object} env - Environment variables containing bot configuration
 * @param {Object} webhookData - Raw Telegram webhook payload
 * @returns {Object} Normalized message data with consistent structure
 */
export function extractMessageData(env, webhookData) {
	const message = webhookData.message || (webhookData.callback_query && webhookData.callback_query.message);
	const chatId = message?.chat?.id;
	let text = message?.text || '';
	const photo = message?.photo || [];
	const document = message?.document || {};
	const replyToMessage = message?.reply_to_message;
	const replyToPhoto = replyToMessage?.photo || [];
	const replyToDocument = replyToMessage?.document || {};

	// For media messages, use the caption as the text content
	// For text-only messages, use the text directly
	if (photo.length > 0 || document.file_id) {
		text = message?.caption || '';
	}

	// Remove bot username mentions from the text to focus on actual content
	const botUsername = env.TELEGRAM_BOT_USERNAME;
	const mentionRegex = new RegExp(`@${botUsername}\\b`, 'gi');
	text = text.replace(mentionRegex, '').trim();

	const userId = message?.from?.id;
	const username = message?.from?.username || 'unknown';
	const firstName = message?.from?.first_name || '';
	const lastName = message?.from?.last_name || '';
	const messageId = message?.message_id;
	const messageDate = message?.date;
	const chatType = message?.chat?.type;
	const mediaGroupId = message?.media_group_id;

	return {
		message, // Original message object for downstream processing
		chatId, // Telegram chat identifier
		text, // Message text content (cleaned of bot mentions)
		photo, // Array of photo objects (if any)
		document, // Document object (if any)
		userId, // Telegram user identifier
		username, // Telegram username (or 'unknown')
		firstName, // User's first name
		lastName, // User's last name
		messageId, // Unique message identifier
		messageDate, // Unix timestamp of message
		chatType, // Type of chat ('private', 'group', 'supergroup', etc.)
		mediaGroupId, // Identifier for grouped media (if applicable)
		replyToMessage, // Original message object that was replied to
		replyToPhoto, // Photo in replied-to message (if any)
		replyToDocument, // Document in replied-to message (if any)
	};
}

/**
 * Determines whether an incoming message should be processed by the bot.
 * Implements business logic for message filtering based on content type,
 * chat context, and bot mention requirements.
 *
 * @param {Object} messageData - Normalized message data from extractMessageData()
 * @param {string} botUsername - Bot's Telegram username
 * @param {Object} webhookData - Raw Telegram webhook payload
 * @returns {boolean} Whether the message should be processed
 */
export function shouldProcessMessage(messageData, botUsername, webhookData) {
	// Extract core message properties for easier access
	const { chatId, text, photo, document, chatType, mediaGroupId, replyToPhoto, replyToDocument } = messageData;

	if (!chatId) {
		console.log('Skipping processing: No valid chat ID.');
		return false;
	}

	// Skip media group messages that lack a caption to process
	if (mediaGroupId && !text) {
		console.log(`Skipping processing for chat ${chatId}: Media group message without caption.`);
		return false;
	}

	// Verify the message contains processable content
	// This includes text, photos, documents, or content in replied-to messages
	const hasContent = photo.length > 0 || document.file_id || text || replyToPhoto.length > 0 || replyToDocument.file_id;
	if (!hasContent) {
		console.log(`Skipping processing for chat ${chatId}: No text, photo, document, or replied-to media content.`);
		return false;
	}

	// Process messages with media (current or replied-to) without requiring a mention
	if (photo.length > 0 || document.file_id || replyToPhoto.length > 0 || replyToDocument.file_id) {
		return true;
	}

	// In private chats, process all messages with content
	if (chatType === 'private') {
		return true;
	}

	// For text-only messages in group chats, require a bot mention
	if (text) {
		const isBotMentioned = checkBotMention(webhookData, botUsername);
		if (!isBotMentioned) {
			console.log(`Skipping processing for chat ${chatId}: Bot not mentioned in group chat.`);
			return false;
		}
		return true;
	}

	console.log(`Skipping processing for chat ${chatId}: Unhandled case.`);
	return false;
}
