import { getRedisClient } from '../../../redis/redisClient.js';
import { TaskManager } from '../../tasks/taskManager.js';
import { sendLongTelegramMessage } from '../../telegram/index.js';
import { escapeHtml } from '../../telegramUtils.js';

/**
 * Extracts the argument from a command string.
 * @param {string} text - The full command text (e.g., "/done buy milk").
 * @param {string} command - The command itself (e.g., "/done").
 * @returns {string} The argument, or an empty string if not present.
 */
function getCommandArgument(text, command) {
	return text.substring(command.length).trim();
}

/**
 * Handles the complete task command by marking a task as completed.
 *
 * @param {Object} env - Environment variables
 * @param {string} chatId - The ID of the chat
 * @param {string} userId - The ID of the user
 * @param {string} text - The full command text
 * @returns {Promise<boolean>} True if the command was processed successfully, false otherwise
 */
export async function handleCompleteTaskCommand(env, chatId, userId, text) {
	try {
		const taskDescription = getCommandArgument(text, '/done');
		if (!taskDescription) {
			await sendLongTelegramMessage(env, chatId, 'Please specify which task to complete. Usage: `/done [task description]`');
			return false;
		}

		const redisClient = getRedisClient(env);
		const taskManager = new TaskManager(redisClient);
		const completedTask = await taskManager.completeTaskByDescription(userId, taskDescription);

		let responseMessage;
		if (completedTask) {
			responseMessage = `✅ Great job! I've marked "${escapeHtml(completedTask.description)}" as completed.`;
		} else {
			responseMessage = `🤔 I couldn't find a pending task matching "${escapeHtml(taskDescription)}".`;
		}

		await sendLongTelegramMessage(env, chatId, responseMessage, { parseMode: 'HTML' });
		return true;
	} catch (error) {
		console.error(`Error handling /done command for chat ${chatId}:`, error);
		await sendLongTelegramMessage(env, chatId, "❌ Sorry, I couldn't complete that task right now.", { parseMode: 'HTML' });
		return false;
	}
}