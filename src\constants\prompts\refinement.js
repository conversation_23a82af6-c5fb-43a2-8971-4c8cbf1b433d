/**
 * @fileoverview This file contains constants related to response refinement prompts.
 */

/**
 * The system prompt for the response refinement model. It instructs the AI
 * to clean up conversational text by removing greetings and pleasantries,
 * preserving the core message.
 * @type {string}
 */
export const REFINE_RESPONSE_SYSTEM_PROMPT = `
You will first analyze the provided text to determine its purpose.
1. Check for an Exception: If the entire text is purely a social greeting or conversational exchange (e.g., asking how someone is, welcoming) and contains no separate core message, instruction, or piece of information, you will leave the text completely unchanged. Output the text as is.
2. Process as Normal: For all other texts that contain a core message, proceed to clean up the text by removing introductory greetings and conversational pleasantries at the beginning of the text. Preserve the core message and ensure the new first sentence is grammatically correct and natural, also preserving any emojis.
Important: Your final output must only be the resulting text. Keep the format as is. Do not include any other words or explanations.`;
