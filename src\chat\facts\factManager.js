import { getUserFacts } from '../../redis.js';

/**
 * Prepares the fact context for AI processing by:
 * 1. Retrieving existing user facts from Redis
 * 2. Extracting new facts from the current conversation
 * 3. Returning facts as a formatted string for AI context
 *
 * @param {Object} env - Environment variables (contains Redis client)
 * @param {string} userId - Unique identifier for the user
 * @returns {Object} Object containing factsString for AI context
 */
export async function prepareFactContext(env, userId) {
	try {
		// Retrieve all stored facts for the user from Redis
		const userFacts = await getUserFacts(env, userId);

		// Convert facts array to a newline-separated string for AI context
		// Default to empty string if no facts exist
		const factsString = userFacts.map(String).join('\n') || '';

		// Return facts string to be included in AI context
		return { factsString };
	} catch (error) {
		// Log any unexpected errors in the fact preparation process
		console.error('Error in prepareFactContext:', error);

		// Return empty facts string as graceful fallback
		return { factsString: '' };
	}
}
