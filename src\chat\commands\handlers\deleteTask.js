import { getRedisClient } from '../../../redis/redisClient.js';
import { TaskManager } from '../../tasks/taskManager.js';
import { sendLongTelegramMessage } from '../../telegram/index.js';
import { escapeHtml } from '../../telegramUtils.js';

/**
 * Extracts the argument from a command string.
 * @param {string} text - The full command text (e.g., "/delete_task buy milk").
 * @param {string} command - The command itself (e.g., "/delete_task").
 * @returns {string} The argument, or an empty string if not present.
 */
function getCommandArgument(text, command) {
	return text.substring(command.length).trim();
}

/**
 * Handles the delete task command by removing a task.
 *
 * @param {Object} env - Environment variables
 * @param {string} chatId - The ID of the chat
 * @param {string} userId - The ID of the user
 * @param {string} text - The full command text
 * @returns {Promise<boolean>} True if the command was processed successfully, false otherwise
 */
export async function handleDeleteTaskCommand(env, chatId, userId, text) {
	try {
		const taskDescription = getCommandArgument(text, '/delete_task');
		if (!taskDescription) {
			await sendLongTelegramMessage(env, chatId, 'Please specify which task to delete. Usage: `/delete_task [task description]`');
			return false;
		}

		const redisClient = getRedisClient(env);
		const taskManager = new TaskManager(redisClient);
		const success = await taskManager.deleteTaskByDescription(userId, taskDescription);

		let responseMessage;
		if (success) {
			responseMessage = `🗑️ Okay, I've deleted the task matching "${escapeHtml(taskDescription)}".`;
	} else {
			responseMessage = `🤔 I couldn't find a pending task matching "${escapeHtml(taskDescription)}".`;
		}

		await sendLongTelegramMessage(env, chatId, responseMessage, { parseMode: 'HTML' });
		return true;
	} catch (error) {
		console.error(`Error handling /delete_task command for chat ${chatId}:`, error);
		await sendLongTelegramMessage(env, chatId, "❌ Sorry, I couldn't delete that task right now.", { parseMode: 'HTML' });
		return false;
	}
}