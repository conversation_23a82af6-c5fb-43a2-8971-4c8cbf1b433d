import { getRedisClient } from './redisClient.js';

/**
 * Retrieves a media group from Redis by its ID.
 * Media groups are stored as lists in Redis with keys formatted as 'mediaGroup:{mediaGroupId}'.
 * @param {object} env - Cloudflare Worker environment variables
 * @param {string} mediaGroupId - The ID of the media group to retrieve
 * @returns {Promise<Array>} Array of file information objects, or empty array if not found or error occurs
 */
export const getMediaGroup = async (env, mediaGroupId) => {
	const redis = getRedisClient(env);
	// Format the Redis key for the media group
	const mediaGroupKey = `mediaGroup:${mediaGroupId}`;
	try {
		// Retrieve all elements from the Redis list
		const fileInfoObjects = await redis.lrange(mediaGroupKey, 0, -1);
		// If already parsed, just filter out null/undefined
		return (fileInfoObjects || []).filter((item) => item != null);
	} catch (err) {
		// Log error and return empty array if retrieval fails
		console.error(`Redis lrange error for key ${mediaGroupKey}:`, err);
		return [];
	}
};
