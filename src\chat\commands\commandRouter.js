import { handleClearHistoryCommand } from './handlers/clearHistory.js';
import { handleCompleteTaskCommand } from './handlers/completeTask.js';
import { handleDeleteTaskCommand } from './handlers/deleteTask.js';
import { handleListTasksCommand } from './handlers/listTasks.js';

/**
 * Processes a detected command by routing it to the appropriate handler function.
 *
 * This function acts as a command router, mapping command types to their
 * corresponding handler functions. It follows the Open/Closed Principle by
 * allowing new commands to be added without modifying existing code.
 *
 * @param {string} commandType - The type of command to process (e.g., 'CLEAR_HISTORY')
 * @param {Object} env - Environment variables
 * @param {Object} messageData - The message data containing chat and user information
 * @param {string} botUsername - The bot's username for context
 * @param {string} text - The full command text (for commands that need arguments)
 * @returns {Promise<boolean>} True if the command was processed successfully, false otherwise
 *
 * @example
 * await processCommand('CLEAR_HISTORY', env, messageData, 'mybot', '/clear');
 */
export async function processCommand(commandType, env, messageData, botUsername, text) {
	const { chatId, userId } = messageData;

	// Route the command to the appropriate handler function
	switch (commandType) {
		case 'CLEAR_HISTORY':
			return await handleClearHistoryCommand(env, chatId, userId, botUsername);
		case 'LIST_TASKS':
			return await handleListTasksCommand(env, chatId, userId);
		case 'COMPLETE_TASK':
			return await handleCompleteTaskCommand(env, chatId, userId, text);
	case 'DELETE_TASK':
			return await handleDeleteTaskCommand(env, chatId, userId, text);
		default:
			console.warn(`Unknown command type: ${commandType}`);
			return false;
	}
}