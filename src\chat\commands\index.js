import { detectCommand } from './commandDetector.js';
import { processCommand } from './commandRouter.js';

/**
 * Main entry point for command handling.
 *
 * This function orchestrates the entire command processing flow:
 * 1. Detects if the input text contains a command
 * 2. If a command is detected, processes it using the appropriate handler
 * 3. Returns the result of command processing
 *
 * @param {string} text - The text to check for commands
 * @param {Object} env - Environment variables
 * @param {Object} messageData - The message data containing chat and user information
 * @param {string} botUsername - The bot's username for context
 * @returns {Promise<boolean>} True if a command was detected and processed, false otherwise
 *
 * @example
 * const handled = await handleCommand('/clear', env, messageData, 'mybot');
 * // Returns true if command was processed, false if no command was detected
 */
export async function handleCommand(text, env, messageData, botUsername) {
	const commandType = detectCommand(text);

	if (!commandType) {
		return false;
	}

	console.log(`Command detected: ${commandType} from user ${messageData.userId} in chat ${messageData.chatId}`);

	// Pass the full text to process<PERSON><PERSON>mand to handle arguments
	return await processCommand(commandType, env, messageData, botUsername, text);
}