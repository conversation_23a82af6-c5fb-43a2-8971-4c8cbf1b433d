/**
 * Handles AI response processing and delivery
 * @module aiResponseHandler
 */
import { REFINE_RESPONSE, REFINE_RESPONSE_SYSTEM_PROMPT, REFINE_RESPONSE_TEMPERATURE } from '../../constants/index.js';
import { callFastGenerativeAI } from '../geminiAI.js';
import { formatTimestamp } from '../utils/formatUtils.js';
import { generateSuggestions } from './suggestionGenerator.js';

/**
 * <PERSON>les logging, escaping, and sending the AI response back to Telegram
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string|Array} aiResponseText - The AI response text (can be an array of responses)
 * @param {string|number} originalMessageId - The ID of the original message being responded to
 * @param {string} originalMessageText - The original user message text.
 * @param {string} userFacts - The user's extracted facts.
 * @param {string} conversationHistory - The recent conversation history.
 * @param {Array} [aiResponseThoughts] - Optional array of AI thoughts
 * @returns {Promise<void>}
 */
export async function handle(env, chatId, responseText, originalMessageId, originalMessageText, userFacts, conversationHistory) {
	// Import here to avoid circular dependencies
	const { escapeHtml } = await import('../telegramUtils.js');
	const { sendLongTelegramMessage } = await import('../telegram/index.js');

	try {
		// Refine the AI response if enabled
		if (REFINE_RESPONSE) {
			const refineConfig = {
				temperature: REFINE_RESPONSE_TEMPERATURE,
				systemInstruction: REFINE_RESPONSE_SYSTEM_PROMPT,
				inferenceProvider: 'groq',
				model: 'moonshotai/kimi-k2-instruct',
				traceTags: ['response-refine'],
			};

			const refineContents = [
				{
					role: 'user',
					parts: [{ text: responseText }],
				},
			];

			const refinedResponse = await callFastGenerativeAI(env, refineConfig, refineContents);
			responseText = refinedResponse.text;
		}

		let suggestions = [];
		if (env.ENABLE_SUGGESTIONS === 'true') {
			// Generate suggestions first, as they are part of the message sent to the user.
			suggestions = await generateSuggestions(
				env,
				originalMessageText,
				responseText,
				userFacts,
				conversationHistory,
				formatTimestamp(Date.now() / 1000, env, 'date'),
				formatTimestamp(Date.now() / 1000, env, 'time'),
			);
		}

		// Send the final response to Telegram as soon as it's ready
		await _sendResponse(env, chatId, responseText, originalMessageId, sendLongTelegramMessage, escapeHtml, suggestions);
	} catch (error) {
		console.error('Error in handleAIResponse:', error);
		throw error;
	}
}

/**
 * Sends the AI response to the user
 * @private
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string} responseText - The response text to send
 * @param {string|number} originalMessageId - The original message ID
 * @param {Function} sendMessage - Function to send the message
 * @param {Function} escapeFn - Function to escape and format text (HTML or Markdown)
 * @returns {Promise<void>}
 */
async function _sendResponse(env, chatId, responseText, originalMessageId, sendMessage, escapeFn, suggestions = []) {
	if (!responseText) {
		console.warn('Empty response text, nothing to send');
		return;
	}

	const formattedResponse = escapeFn(responseText);

	try {
		const messageOptions = { message_id: originalMessageId, parseMode: 'HTML' };
		const sendResult = await sendMessage(env, chatId, formattedResponse, messageOptions, suggestions);

		if (sendResult) {
			console.log(`Successfully sent AI response to chat ${chatId}`);
		} else {
			console.error(`Failed to send AI response to chat ${chatId}`);
		}
	} catch (error) {
		console.error(`Error sending AI response to chat ${chatId}:`, error);
		throw error;
	}
}
