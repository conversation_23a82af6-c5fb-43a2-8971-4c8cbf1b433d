# Qwen Code Context for `harmony-chat`

This document provides essential context about the `harmony-chat` project for Qwen Code.

## Project Overview

`harmony-chat` is a high-performance, AI-powered Telegram bot named Harmony, built on Cloudflare Workers. It leverages Google's Gemini AI to provide context-aware, conversational responses. The bot integrates with Redis for storing conversation history and user facts, and includes advanced features like media processing, proactive messaging, and smart message filtering. It is designed with a serverless architecture for scalability and performance.

### Key Features

- **AI-Powered Conversations**: Powered by Google Gemini AI with a custom personality.
- **Context Awareness**: Maintains conversation history and learns user facts.
- **Media Support**: Processes photos, documents, and media groups.
- **Proactive Messaging**: Can generate and send "thoughts" or monologues.
- **Access Control**: User whitelisting for restricted access.
- **Smart Filtering**: Handles mentions, private messages, and media.
- **Typing Indicators**: Shows realistic typing status.
- **Multi-language**: Primarily Indonesian with Japanese phrases.

## Technology Stack

- **Runtime**: Cloudflare Workers (serverless)
- **Framework**: Hono.js (for routing)
- **Language**: JavaScript
- **AI/LLM**: Google Gemini AI via LangChain
- **Databases**:
    - Upstash Redis (caching, history)
    - Upstash Vector (semantic search, embeddings)
- **Monitoring**: Langsmith

## Project Structure

```
harmony-chat/
├── src/                  # Source code
│   ├── index.js          # Entry point
│   ├── routes/           # API route definitions
│   ├── middleware/       # Custom Hono middleware
│   ├── chat/             # Core chat logic (message processing, commands)
│   ├── redis.js          # Redis interaction logic
│   ├── utils/            # Utility functions (e.g., webhook helpers)
│   └── workers/          # Background workers (e.g., reminders)
├── docs/                 # Documentation files
├── examples/             # Example files (e.g., HTTP requests)
├── package.json          # Node.js dependencies and scripts
├── wrangler.toml         # Cloudflare Workers configuration
├── harmony.http          # Example HTTP requests for testing
├── AGENTS.md             # Guidelines for code style, linting, etc.
├── README.md             # Project documentation
└── ...                   # Config files (.gitignore, .prettierrc, etc.)
```

## Development Environment

### Prerequisites

- Node.js 18+
- npm or yarn
- Cloudflare account with Workers enabled

### Initial Setup

1.  Clone the repository.
2.  Run `npm install` to install dependencies.

### Configuration

Environment variables and secrets are managed via `wrangler.toml`. Key variables include:

- `TELEGRAM_BOT_USERNAME`: The Telegram bot's username.
- `WHITELISTED_USERS` (or `DISABLE_USER_WHITELIST`): Controls access.
- `DEV_MODE`: Toggles development features.
- Secrets (like `TELEGRAM_BOT_TOKEN` and Upstash credentials) are configured using `wrangler secrets`.

### Local Development

- Start the development server with `npm run dev`.
- For public access (e.g., for webhook testing), use `npm run dev:public`.

### Code Style & Linting

- Code style is enforced by Prettier (`.prettierrc`) and ESLint (`eslint.config.mjs`).
- Refer to `AGENTS.md` for specific guidelines on imports, naming, error handling, etc.
    - **Indentation**: Tabs
    - **Quotes**: Single quotes
    - **Semicolons**: Required
    - **Naming**: kebab-case for files, camelCase for variables, UPPER_SNAKE_CASE for constants.

### Building and Deployment

Deployment is handled by Cloudflare Wrangler.

- Deploy to production with `npm run deploy`.

## Key Functional Areas

### Core Application Logic (`src/index.js`)

- Initializes the Hono app.
- Registers global middleware (error handling, security headers, CORS, request validation, user whitelist).
- Mounts all API routes.

### API Endpoints

Defined in `src/routes/`.

- `GET /health`: Health check.
- `GET /ping`: Simple ping.
- `POST /hrmny`: Main Telegram webhook endpoint. It parses incoming messages, logs them to Redis/vector DB, and triggers background processing.
- `POST /thoughts`: Endpoint for the bot to send proactive messages.

### Telegram Integration (`src/routes/telegram.routes.js`)

- The `/hrmny` route is the primary entry point for Telegram messages.
- It uses helper functions (`src/utils/telegramWebhookHelpers.js`) to parse and validate incoming data.
- Messages are logged and stored in Upstash Vector and Redis.
- Actual AI response generation and sending happens in `src/chat/messageProcessor.js` in the background using `c.executionCtx.waitUntil`.

### AI and Chat Logic (`src/chat/`)

- Orchestrated by `messageProcessor.js`.
- Uses LangChain with Google Generative AI (`@langchain/google-genai`) and custom prompts for Harmony's personality.
- Integrates with Upstash Redis for history and Upstash Vector for semantic search/context retrieval.
- Includes `commandHandler.js` for command detection.
- Includes `factManager.js` for extracting and managing user facts.

### Data Storage

- **Redis (`src/redis.js`)**: Used for storing conversation history and temporary data.
- **Upstash Vector (`src/utils/vectorStorage.js`)**: Used for storing message embeddings for semantic search.

## Important Notes for Development

- **Serverless Constraints**: Optimize for Cloudflare Workers' environment (cold starts, execution limits).
- **Async Operations**: Use `c.executionCtx.waitUntil` for background tasks that should not block the HTTP response.
- **Error Handling**: Middleware and route handlers have specific error handling.
- **Environment Variables**: Access via `c.env`.
